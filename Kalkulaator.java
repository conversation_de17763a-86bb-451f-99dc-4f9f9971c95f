import java.util.Scanner;

public class Kalkulaator {
    public static void main(String[] args) {
        Scanner s = new Scanner(System.in);
        
        System.out.print("Sisesta esimene arv: ");
        double a = s.nextDouble();
        
        System.out.print("Sisesta tehe (+, -, *, /): ");
        char op = s.next().charAt(0);
        
        System.out.print("Sisesta teine arv: ");
        double b = s.nextDouble();
        
        double tulemus = 0;
        
        switch(op) {
            case '+': tulemus = a + b; break;
            case '-': tulemus = a - b; break;
            case '*': tulemus = a * b; break;
            case '/': 
                if(b != 0) tulemus = a / b;
                else { System.out.println("Nulliga jagamine!"); return; }
                break;
            default: System.out.println("Vigane tehe!"); return;
        }
        
        System.out.println("Tulemus: " + tulemus);
        s.close();
    }
}