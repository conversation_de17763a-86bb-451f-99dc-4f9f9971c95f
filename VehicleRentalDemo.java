// Abstraktne baasklass kõigile sõidukitele
abstract class Vehicle {
    protected String registrationNumber;
    protected String brand;
    protected int year;
    protected double baseRentalPrice;
    
    public Vehicle(String registrationNumber, String brand, int year, double baseRentalPrice) {
        this.registrationNumber = registrationNumber;
        this.brand = brand;
        this.year = year;
        this.baseRentalPrice = baseRentalPrice;
    }
    
    // Abstraktne meetod - iga sõiduk arvutab renti erinevalt
    public abstract double calculateDailyRent(int days);
    
    // Abstraktne meetod - iga sõiduki tüüp vajab erinevat hooldust
    public abstract String getMaintenanceInfo();
    
    // Ühine meetod kõigile sõidukitele
    public String getBasicInfo() {
        return String.format("%s %s (%d) - %s", brand, getClass().getSimpleName(), year, registrationNumber);
    }
    
    // Getterid
    public String getRegistrationNumber() { return registrationNumber; }
    public String getBrand() { return brand; }
    public int getYear() { return year; }
    public double getBaseRentalPrice() { return baseRentalPrice; }
}

// Sõiduautod
class Car extends Vehicle {
    private int passengerCount;
    private boolean hasAirConditioning;
    
    public Car(String registrationNumber, String brand, int year, double baseRentalPrice, 
               int passengerCount, boolean hasAirConditioning) {
        super(registrationNumber, brand, year, baseRentalPrice);
        this.passengerCount = passengerCount;
        this.hasAirConditioning = hasAirConditioning;
    }
    
    @Override
    public double calculateDailyRent(int days) {
        double dailyRate = baseRentalPrice;
        
        // Kliimaseade lisatasu
        if (hasAirConditioning) {
            dailyRate += 5.0;
        }
        
        // Rohkemate kohtade eest lisatasu
        if (passengerCount > 5) {
            dailyRate += 10.0;
        }
        
        // Pikemale perioodile allahindlus
        if (days > 7) {
            dailyRate *= 0.9; // 10% allahindlus
        }
        
        return dailyRate * days;
    }
    
    @Override
    public String getMaintenanceInfo() {
        return "Vajab õlivahetus iga 15000 km, rehvivahetust 2x aastas";
    }
    
    public int getPassengerCount() { return passengerCount; }
    public boolean hasAirConditioning() { return hasAirConditioning; }
}

// Veoautod
class Truck extends Vehicle {
    private double cargoCapacity; // tonnides
    private boolean hasHydraulicLift;
    
    public Truck(String registrationNumber, String brand, int year, double baseRentalPrice,
                 double cargoCapacity, boolean hasHydraulicLift) {
        super(registrationNumber, brand, year, baseRentalPrice);
        this.cargoCapacity = cargoCapacity;
        this.hasHydraulicLift = hasHydraulicLift;
    }
    
    @Override
    public double calculateDailyRent(int days) {
        double dailyRate = baseRentalPrice;
        
        // Suurema kandevõime eest lisatasu
        dailyRate += cargoCapacity * 15.0;
        
        // Hüdraulilise tõstuki eest lisatasu
        if (hasHydraulicLift) {
            dailyRate += 25.0;
        }
        
        // Lühikeste perioodide eest lisatasu (veoautod on keerulisemad hallata)
        if (days < 3) {
            dailyRate *= 1.3;
        }
        
        return dailyRate * days;
    }
    
    @Override
    public String getMaintenanceInfo() {
        return "Vajab raskeveokite hooldust iga 10000 km, piduri kontroll iga 6 kuud";
    }
    
    public double getCargoCapacity() { return cargoCapacity; }
    public boolean hasHydraulicLift() { return hasHydraulicLift; }
}

// Mootorrattad
class Motorcycle extends Vehicle {
    private int engineSize; // kubiksentimeetrites
    private boolean isOffRoad;
    
    public Motorcycle(String registrationNumber, String brand, int year, double baseRentalPrice,
                      int engineSize, boolean isOffRoad) {
        super(registrationNumber, brand, year, baseRentalPrice);
        this.engineSize = engineSize;
        this.isOffRoad = isOffRoad;
    }
    
    @Override
    public double calculateDailyRent(int days) {
        double dailyRate = baseRentalPrice;
        
        // Suurema mootori eest lisatasu
        if (engineSize > 500) {
            dailyRate += 20.0;
        }
        
        // Maastikumootorrattad on riskantseimad
        if (isOffRoad) {
            dailyRate += 15.0;
        }
        
        // Mootorrattad on odavamad pikemaks perioodiks
        if (days > 14) {
            dailyRate *= 0.8; // 20% allahindlus
        }
        
        return dailyRate * days;
    }
    
    @Override
    public String getMaintenanceInfo() {
        return "Kett ja hammasrattad iga 5000 km, kiiver kontroll enne iga väljalaenu";
    }
    
    public int getEngineSize() { return engineSize; }
    public boolean isOffRoad() { return isOffRoad; }
}

// Rendiettevõtte süsteem
class RentalSystem {
    private java.util.List<Vehicle> vehicles;

    public RentalSystem() {
        this.vehicles = new java.util.ArrayList<>();
    }

    public void addVehicle(Vehicle vehicle) {
        vehicles.add(vehicle);
        System.out.println("Lisatud: " + vehicle.getBasicInfo());
    }

    public void generateRentalQuote(int days) {
        System.out.println("\n=== HINNAPAKKUMINE " + days + " PÄEVAKS ===");
        for (Vehicle vehicle : vehicles) {
            double price = vehicle.calculateDailyRent(days);
            System.out.printf("%s - %.2f€%n", vehicle.getBasicInfo(), price);
        }
    }

    public void printMaintenanceSchedule() {
        System.out.println("\n=== HOOLDUSPLAAN ===");
        for (Vehicle vehicle : vehicles) {
            System.out.println(vehicle.getBasicInfo() + ":");
            System.out.println("  " + vehicle.getMaintenanceInfo());
        }
    }

    public void printVehiclesByType(Class<?> vehicleType) {
        System.out.println("\n=== " + vehicleType.getSimpleName().toUpperCase() + " SÕIDUKID ===");
        for (Vehicle vehicle : vehicles) {
            if (vehicleType.isInstance(vehicle)) {
                System.out.println(vehicle.getBasicInfo());
                if (vehicle instanceof Car) {
                    Car car = (Car) vehicle;
                    System.out.println("  Kohti: " + car.getPassengerCount() +
                                     ", Kliima: " + (car.hasAirConditioning() ? "Jah" : "Ei"));
                } else if (vehicle instanceof Truck) {
                    Truck truck = (Truck) vehicle;
                    System.out.println("  Kandevõime: " + truck.getCargoCapacity() + "t" +
                                     ", Tõstuk: " + (truck.hasHydraulicLift() ? "Jah" : "Ei"));
                } else if (vehicle instanceof Motorcycle) {
                    Motorcycle bike = (Motorcycle) vehicle;
                    System.out.println("  Mootor: " + bike.getEngineSize() + "cc" +
                                     ", Maastik: " + (bike.isOffRoad() ? "Jah" : "Ei"));
                }
            }
        }
    }
}

// Peaklass demonstratsiooni jaoks
public class VehicleRentalDemo {
    public static void main(String[] args) {
        RentalSystem rentalSystem = new RentalSystem();
        
        // Lisame erinevaid sõidukeid
        rentalSystem.addVehicle(new Car("123ABC", "Toyota Corolla", 2020, 35.0, 5, true));
        rentalSystem.addVehicle(new Car("456DEF", "BMW X5", 2022, 75.0, 7, true));
        rentalSystem.addVehicle(new Car("789GHI", "Volkswagen Up!", 2019, 25.0, 4, false));
        
        rentalSystem.addVehicle(new Truck("111TRK", "Mercedes Sprinter", 2021, 65.0, 3.5, true));
        rentalSystem.addVehicle(new Truck("222TRK", "Iveco Daily", 2020, 55.0, 2.5, false));
        
        rentalSystem.addVehicle(new Motorcycle("333MOT", "Honda CBR", 2023, 40.0, 600, false));
        rentalSystem.addVehicle(new Motorcycle("444MOT", "KTM Adventure", 2022, 45.0, 790, true));
        
        // Demonstreerime polümorfismi - sama meetodid töötavad erinevate objektidega
        
        // 1. Hinnapakkumised erinevateks perioodideks
        rentalSystem.generateRentalQuote(1);   // 1 päev
        rentalSystem.generateRentalQuote(7);   // 1 nädal
        rentalSystem.generateRentalQuote(30);  // 1 kuu
        
        // 2. Hooldusplaan
        rentalSystem.printMaintenanceSchedule();
        
        // 3. Sõidukid tüübi järgi
        rentalSystem.printVehiclesByType(Car.class);
        rentalSystem.printVehiclesByType(Truck.class);
        rentalSystem.printVehiclesByType(Motorcycle.class);
        
        System.out.println("\n=== POLÜMORFISMI NÄIDE ===");
        System.out.println("Sama massiiv, erinevad objektid, erinevad käitumised:");
        
        // Näitame kuidas sama kood töötab erinevate objektidega
        Vehicle[] mixedVehicles = {
            new Car("TEST1", "Test Auto", 2020, 30.0, 5, true),
            new Truck("TEST2", "Test Veokas", 2020, 50.0, 5.0, true),
            new Motorcycle("TEST3", "Test Mootorratas", 2020, 35.0, 500, false)
        };
        
        for (Vehicle v : mixedVehicles) {
            System.out.println(v.getBasicInfo() + " - 5 päeva: " + v.calculateDailyRent(5) + "€");
        }
    }
}